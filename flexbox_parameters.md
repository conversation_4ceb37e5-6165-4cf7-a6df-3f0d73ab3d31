# Flexbox 参数复习：flex-grow、flex-shrink、flex-basis

这个文档帮助你快速复习 Flexbox 中三个关键参数的含义和作用：

---

## 1. `flex-grow`

- **定义**：决定子元素在容器有剩余空间时，能否以及按何种比例“放大”以填充剩余空间。
- **默认值**：`0`（不分配额外空间）。
- **典型用法**：
  - `flex-grow: 0;` —— 即使父容器有剩余空间，该项也不会放大。
  - `flex-grow: 1;` —— 按比例分配所有剩余空间，若多个子项都设置为 1，则平分剩余空间。

> **示例**：
> ```css
> .item {
>   flex-grow: 1;
> }
> ```
> 这样每个 `.item` 都会拉伸，平分容器中多余的宽度。

---

## 2. `flex-shrink`

- **定义**：决定子元素在空间不足时，是否以及按何种比例“收缩”以适应容器。
- **默认值**：`1`（允许按比例收缩）。
- **典型用法**：
  - `flex-shrink: 1;` —— 在空间不足时，该项会按比例收缩。
  - `flex-shrink: 0;` —— 在空间不足时，该项不会收缩，可能导致溢出。

> **示例**：
> ```css
> .item {
>   flex-shrink: 0;
> }
> ```
> 这样即便容器空间变小，`.item` 保持初始大小，不会收缩。

---

## 3. `flex-basis`

- **定义**：指定在分配额外空间或收缩之前，子元素的初始基准尺寸。
- **默认值**：`auto`，意味着使用元素本身的主尺寸（例如 `width`）或内容大小作为基准。
- **典型用法**：
  - `flex-basis: auto;` —— 以元素的 `width` 或内容大小为初始尺寸。
  - `flex-basis: 200px;` —— 明确设定初始基准为 200px。

> **示例**：
> ```css
> .item {
>   flex-basis: 150px;
> }
> ```
> 这样 `.item` 在伸缩前的初始宽度就是 150px。

---

## 默认简写 `flex`

浏览器默认会给所有 flex 项设置：

```css
flex: 0 1 auto;
```

对应：
- `flex-grow: 0`  
- `flex-shrink: 1`  
- `flex-basis: auto`

---

> **小提示**：如果你希望某个子项在剩余空间中也能拉伸，可以简单写 `flex: 1;`，相当于 `flex: 1 1 auto;`。

> **练习答案示例**：
> - 如果你想让设置按钮在剩余空间里也能拉伸，可以给它写 `flex: 1;`（即 `flex: 1 1 auto`），这样按钮就会占据输入框腾出的那部分空间，与输入框一起平分布局宽度。

---

## 练习：初始基准总宽度计算

假设有如下场景：

- 父容器宽度：300px
- 子元素：
  1. 输入框（`.search-input`），
     - `width: 100%`，
     - `flex-basis: auto`（基准宽度 = 300px）
  2. 设置按钮，固定宽度 20px
  3. 清除按钮，固定宽度 20px

**问题**：
1. 这三个子元素的“初始基准总宽度”是多少？
2. 当初始总宽度（340px）大于容器宽度（300px）时，浏览器会如何处理？

**答案要点**：
- 初始基准总宽度：300px + 20px + 20px = **340px**。
- 浏览器会因空间不足按照每个子项的 `flex-shrink: 1` 比例收缩所有子项，将总宽度压缩到 300px。  
- 收缩后，各项宽度按比例计算：
  - 输入框宽度 ≈ (300 / 340) × 300
  - 每个按钮宽度 ≈ (300 / 340) × 20

---

> **补充**：
> “直接子元素”指的是在父容器下与 `display: flex;` 同级的元素，即：
> ```html
> <div class="container" style="display:flex">
>   <input>    <!-- 这里是 flex 项（flex 子项） -->
>   <button>   <!-- 这里也是 -->
>   <span>
>     <i></i>  <!-- 这两个不是，除非 <span> 也设了 display:flex -->
>   </span>
> </div>
> ```

---

*复习完成，祝使用愉快！*

